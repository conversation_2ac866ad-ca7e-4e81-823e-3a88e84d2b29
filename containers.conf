# Buildah configuration for rootless operation in Kubernetes
[containers]
# Use vfs storage driver for compatibility in K8s
default_storage_driver = "vfs"

# Disable seccomp for compatibility
seccomp_profile = ""

# Use chroot isolation for rootless
isolation = "chroot"

# Disable cgroups management
cgroups = "disabled"

# Set default capabilities
default_capabilities = [
  "CHOWN",
  "DAC_OVERRIDE", 
  "FOWNER",
  "FSETID",
  "KILL",
  "NET_BIND_SERVICE",
  "SETFCAP",
  "SETGID",
  "SETPCAP",
  "SETUID",
  "SYS_CHROOT"
]

[storage]
# Use vfs driver for maximum compatibility
driver = "vfs"

# Set storage paths to writable locations
runroot = "/tmp/containers/storage"
graphroot = "/tmp/containers/storage"

[storage.options]
# Disable quota management
ignore_chown_errors = "true"
