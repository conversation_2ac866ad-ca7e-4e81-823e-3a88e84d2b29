# Use Python 3.11 as base image
FROM python:3.11

# Set new user
RUN useradd appuser && mkdir /app && chown -R appuser /app

# Set working directory
WORKDIR /app

# Copy requirements.txt with no write permissions
COPY requirements.txt .

# Upgrade pip
RUN pip install --upgrade pip

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy script with execute permissions
COPY netbox_ip_scan.py .

# Copy certs with no write permissions
COPY certs/* /tmp

# Add FreeIPA CA to Python's certifi
RUN echo "" >> /usr/local/lib/python3.11/site-packages/certifi/cacert.pem && cat /tmp/freeipa-ca.crt >> /usr/local/lib/python3.11/site-packages/certifi/cacert.pem

# Fix permissions
RUN chown -R appuser /app && chmod 755 /app/netbox_ip_scan.py

# Set user
USER root
