# Миграция с Kaniko на Buildah

## Обзор изменений

Данная конфигурация заменяет Kaniko на Buildah для сборки Docker образов в GitLab CI с учетом следующих требований:
- `--tls-verify=false` для работы с самоподписанными сертификатами
- Работа без привилегий (rootless)
- Совместимость с GitLab Runner в Kubernetes

## Ключевые изменения

### 1. Замена образа
- **Было**: `gcr.io/kaniko-project/executor:v1.9.0-debug`
- **Стало**: `quay.io/buildah/stable:latest`

### 2. Конфигурация хранилища
- Использование VFS storage driver для максимальной совместимости
- Настройка путей хранения в `/tmp/containers/storage`
- Изоляция через chroot вместо пространств имен

### 3. Аутентификация
- Сохранение совместимости с Docker config format
- Поддержка пользовательских CA сертификатов
- Использование `--authfile` для аутентификации в registry

### 4. Команды сборки
- `buildah bud` для сборки образа
- `buildah push` для отправки в registry
- Флаг `--tls-verify=false` для всех операций с registry

## Преимущества Buildah

1. **Rootless операции**: Не требует привилегированного режима
2. **Совместимость**: Лучше работает в ограниченных средах Kubernetes
3. **Гибкость**: Больше опций конфигурации для различных сред
4. **Производительность**: VFS driver обеспечивает стабильную работу

## Конфигурация

Все настройки Buildah создаются динамически в GitLab CI:

- VFS storage driver
- Отключение cgroups
- Настройка capabilities
- Изоляция через chroot
- Конфигурация создается inline в `before_script`

## Переменные окружения

Убедитесь, что следующие переменные настроены в GitLab CI:

- `HARBOR_HOST` - адрес Harbor registry
- `HARBOR_USERNAME` - имя пользователя для registry
- `HARBOR_PASSWORD` - пароль для registry
- `CA_CERT` - содержимое CA сертификата

## Устранение неполадок

### Проблемы с хранилищем

Если возникают ошибки с хранилищем, проверьте:

- Доступность `/tmp/containers/storage`
- Права доступа к директориям
- Настройки VFS driver

**Ошибка "permission denied" при ApplyLayer:**
Эта ошибка возникает в Kubernetes при попытке монтирования. Решается:

- Использованием `--layers=false` для отключения слоев
- Настройкой `mount_program = ""` в конфигурации
- Добавлением `ignore_chown_errors = "true"`

### Проблемы с сетью

При ошибках подключения к registry:

- Убедитесь, что `--tls-verify=false` используется
- Проверьте настройки CA сертификатов
- Проверьте доступность Harbor registry

### Проблемы с аутентификацией

При ошибках аутентификации:

- Проверьте формат `~/.docker/config.json`
- Убедитесь в корректности переменных `HARBOR_USERNAME` и `HARBOR_PASSWORD`
- Проверьте использование `--authfile` в командах push
