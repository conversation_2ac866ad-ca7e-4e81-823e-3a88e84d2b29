stages:
  # - test
  - build

.buildah_auth: &buildah_auth
  before_script:
    # Setup buildah for rootless operation in Kubernetes
    - export BUILDAH_ISOLATION=chroot
    - export STORAGE_DRIVER=vfs
    - export BUILDAH_FORMAT=docker
    - export TMPDIR=/tmp
    - mkdir -p ~/.config/containers
    - mkdir -p /tmp/containers/storage
    - mkdir -p /tmp/buildah
    # Create containers configuration inline
    - |
      cat > ~/.config/containers/containers.conf << EOF
      [containers]
      default_storage_driver = "vfs"
      seccomp_profile = ""
      isolation = "chroot"
      cgroups = "disabled"
      default_capabilities = [
        "CHOWN", "DAC_OVERRIDE", "FOWNER", "FSETID", "KILL",
        "NET_BIND_SERVICE", "SETFCAP", "SETGID", "SETPCAP", "SETUID", "SYS_CHROOT"
      ]

      [storage]
      driver = "vfs"
      runroot = "/tmp/containers/storage"
      graphroot = "/tmp/containers/storage"

      [storage.options]
      ignore_chown_errors = "true"
      mount_program = ""

      [storage.options.vfs]
      ignore_chown_errors = "true"
      EOF
    # Create storage configuration
    - |
      cat > ~/.config/containers/storage.conf << EOF
      [storage]
      driver = "vfs"
      runroot = "/tmp/containers/storage"
      graphroot = "/tmp/containers/storage"

      [storage.options]
      ignore_chown_errors = "true"
      mount_program = ""
      EOF
    # Setup registry authentication
    - mkdir -p ~/.docker
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > ~/.docker/config.json
    # Setup CA certificates
    - mkdir -p /tmp/certs
    - echo "$CA_CERT" > /tmp/certs/ca.crt
    - export SSL_CERT_FILE=/tmp/certs/ca.crt

# include:
#   - project: 'infrastructure/unified-security-pipeline'
#     ref: main
#     file: '.gitlab-ci-module.yml'

build_netbox_ip_scan:
  stage: build
  image:
    name: quay.io/buildah/stable:latest
    entrypoint: [""]
  <<: *buildah_auth
  script:
    - buildah bud
      --tls-verify=false
      --storage-driver=vfs
      --isolation=chroot
      --format=docker
      --layers=false
      --pull=missing
      --file Dockerfile
      --tag netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      --tag netbox-ip-scan:latest
      .
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:$CI_COMMIT_SHORT_SHA
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:latest
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:latest
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
