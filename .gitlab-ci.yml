stages:
  # - test
  - build

.kaniko_auth: &kaniko_auth
  before_script:
    - mkdir -p /kaniko/.docker
    - mkdir -p /kaniko/.docker/certs
    - cat $CA_CERT >> /kaniko/.docker/certs/ca-certificates.crt
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > /kaniko/.docker/config.json

# include:
#   - project: 'infrastructure/unified-security-pipeline'
#     ref: main
#     file: '.gitlab-ci-module.yml'

build_netbox_ip_scan:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.9.0-debug
    entrypoint: [""]
  <<: *kaniko_auth
  script:
    - /kaniko/executor
      --context .
      --dockerfile Dockerfile
      --destination "${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:$CI_COMMIT_SHORT_SHA"
      --destination "${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:latest"
      --cache=true
      --cache-ttl=24h
      --registry-certificate harbor.sl.local=/kaniko/.docker/certs/ca-certificates.crt
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
