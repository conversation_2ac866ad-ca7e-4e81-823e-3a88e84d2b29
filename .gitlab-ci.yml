stages:
  # - test
  - build

.buildah_auth: &buildah_auth
  before_script:
    - export BUILDAH_ISOLATION=chroot
    - export STORAGE_DRIVER=vfs
    - mkdir -p ~/.config/containers
    - mkdir -p /tmp/containers/storage
    - |
      cat > ~/.config/containers/containers.conf << EOF
      [containers]
      default_storage_driver = "vfs"
      seccomp_profile = ""
      isolation = "chroot"
      cgroups = "disabled"
      default_capabilities = [
        "CHOWN", "DAC_OVERRIDE", "FOWNER", "FSETID", "KILL",
        "NET_BIND_SERVICE", "SETFCAP", "SETGID", "SETPCAP", "SETUID", "SYS_CHROOT"
      ]

      [storage]
      driver = "vfs"
      runroot = "/tmp/containers/storage"
      graphroot = "/tmp/containers/storage"

      [storage.options]
      ignore_chown_errors = "true"
      EOF
    - mkdir -p ~/.docker
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > ~/.docker/config.json
    - mkdir -p /tmp/certs
    - echo "$CA_CERT" > /tmp/certs/ca.crt
    - export SSL_CERT_FILE=/tmp/certs/ca.crt

# include:
#   - project: 'infrastructure/unified-security-pipeline'
#     ref: main
#     file: '.gitlab-ci-module.yml'

build_netbox_ip_scan:
  stage: build
  image:
    name: quay.io/buildah/stable:latest
    entrypoint: [""]
  <<: *buildah_auth
  script:
    # Build the image
    - buildah bud
      --tls-verify=false
      --storage-driver=vfs
      --isolation=chroot
      --file Dockerfile
      --tag netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      --tag netbox-ip-scan:latest
      .
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:$CI_COMMIT_SHORT_SHA
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:latest
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:latest
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
