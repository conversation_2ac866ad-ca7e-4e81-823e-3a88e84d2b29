stages:
  # - test
  - build

.buildah_auth: &buildah_auth
  before_script:
    # Setup buildah for rootless operation
    - export BUILDAH_ISOLATION=chroot
    - export STORAGE_DRIVER=vfs
    - mkdir -p ~/.config/containers
    - mkdir -p /tmp/containers/storage
    # Copy containers configuration
    - cp containers.conf ~/.config/containers/containers.conf
    # Setup registry authentication
    - mkdir -p ~/.docker
    - echo "{\"auths\":{\"${HARBOR_HOST}\":{\"auth\":\"$(echo -n ${HARBOR_USERNAME}:${HARBOR_PASSWORD} | base64)\"}}}" > ~/.docker/config.json
    # Setup CA certificates
    - mkdir -p /tmp/certs
    - echo "$CA_CERT" > /tmp/certs/ca.crt
    - export SSL_CERT_FILE=/tmp/certs/ca.crt

# include:
#   - project: 'infrastructure/unified-security-pipeline'
#     ref: main
#     file: '.gitlab-ci-module.yml'

build_netbox_ip_scan:
  stage: build
  image:
    name: quay.io/buildah/stable:latest
    entrypoint: [""]
  <<: *buildah_auth
  script:
    # Build the image
    - buildah bud
      --tls-verify=false
      --storage-driver=vfs
      --isolation=chroot
      --file Dockerfile
      --tag netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      --tag netbox-ip-scan:latest
      .
    # Push images with both tags
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:$CI_COMMIT_SHORT_SHA
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:$CI_COMMIT_SHORT_SHA
    - buildah push
      --tls-verify=false
      --authfile ~/.docker/config.json
      netbox-ip-scan:latest
      docker://${HARBOR_HOST}/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/netbox-ip-scan:latest
  rules:
    - if: $CI_COMMIT_BRANCH == 'master'
