import os
from netaddr import IPNetwork
from netbox import <PERSON>Box
from pythonping import ping
import urllib3
import concurrent.futures
import dns.resolver
import dns.reversename

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

DHCP_POOL = IPNetwork(os.getenv("DHCP_POOL"))

NETBOX_HOST = os.getenv("NETBOX_HOST")
NETBOX_PORT = os.getenv("NETBOX_PORT")
API_TOKEN = os.getenv("API_TOKEN")

nb = NetBox(host=NETBOX_HOST, port=NETBOX_PORT, auth_token=API_TOKEN, use_ssl=True)

def get_dns_name(ip):
    try:
        resolver = dns.resolver.Resolver()
        result = resolver.resolve(dns.reversename.from_address(str(ip)), "PTR")
        dns_name = str(result[0]) if result else None
        
        if dns_name and "clients.your-server.de" not in dns_name and "-connect.me." not in dns_name:
            return dns_name
        else:
            return None
    except dns.resolver.NXDOMAIN:
        return None

def check_ip(ip):
    ip_address = nb.ipam.get_ip_addresses(address=str(ip))
    
    if ip in DHCP_POOL:
        status = "active" if ping(str(ip), count=1, timeout=2).success() else "dhcp"
        description = "Active IP Address" if status == "active" else "DHCP Pool"
    else:
        status = "active" if ping(str(ip), count=1, timeout=2).success() else "reserved"
        description = "Active IP Address" if status == "active" else "Inactive IP Address"
    
    dns_name = get_dns_name(ip)
    
    if dns_name:
        dns_name_field = dns_name
    else:
        dns_name_field = ""
    
    if ip_address:
        nb.ipam.update_ip_by_id(ip_id=ip_address[0]["id"], status=status, description=description, dns_name=dns_name_field)
    else:
        nb.ipam.create_ip_address(address=str(ip), status=status, description=description, dns_name=dns_name_field)
    
    print(f"IP address {ip} is {status}")

prefixes = [IPNetwork(prefix['prefix']) for prefix in nb.ipam.get_ip_prefixes()]

with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
    for prefix in prefixes:
        for ip in prefix:
            executor.submit(check_ip, ip)
